// Performance Optimized JavaScript for Nashop - Mobile First
// Optimized for Bootstrap 5 and better mobile performance

// Performance monitoring
const performanceMetrics = {
    startTime: performance.now(),
    isLowEndDevice: navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2,
    isMobile: window.innerWidth <= 768,
    prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
};

// Debounce function for performance
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function for scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Optimized DOM ready function
function domReady(callback) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', callback);
    } else {
        callback();
    }
}

// Main initialization
domReady(() => {
    console.log('🚀 Nashop Bootstrap 5 - Performance Optimized');
    console.log(`📱 Mobile: ${performanceMetrics.isMobile}`);
    console.log(`⚡ Low-end device: ${performanceMetrics.isLowEndDevice}`);
    console.log(`🎭 Reduced motion: ${performanceMetrics.prefersReducedMotion}`);

    // Initialize core features
    initializeLucideIcons();
    initializeIntersectionObserver();
    initializeOptimizedAnimations();
    initializePerformanceMonitoring();

    // Initialize interactive features only if not low-end device
    if (!performanceMetrics.isLowEndDevice) {
        initializeFingerprintEffects();
        initialize3DEffects();
    }

    // Set current year
    updateCurrentYear();

    // Hide loading screen after initialization
    hideLoadingScreen();

    console.log(`✅ Initialization complete in ${(performance.now() - performanceMetrics.startTime).toFixed(2)}ms`);
});

// Hide loading screen with smooth transition
function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        // Add a small delay to ensure everything is loaded
        setTimeout(() => {
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
                loadingScreen.remove();
            }, 500);
        }, 100);
    }
}

// Initialize Lucide Icons with error handling
function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        try {
            lucide.createIcons();
            console.log('✅ Lucide icons initialized');
        } catch (e) {
            console.error('❌ Lucide icon creation failed:', e);
        }
    } else {
        console.warn('⚠️ Lucide library not loaded');
    }
}

// Optimized Intersection Observer for animations
function initializeIntersectionObserver() {
    if (!('IntersectionObserver' in window)) {
        // Fallback for browsers without IntersectionObserver
        document.querySelectorAll('.animate-fade-in').forEach(el => {
            el.classList.add('is-visible');
        });
        return;
    }

    const observerOptions = {
        threshold: performanceMetrics.isMobile ? 0.05 : 0.1,
        rootMargin: performanceMetrics.isMobile ? '50px' : '100px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('is-visible');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements with animation classes
    document.querySelectorAll('.animate-fade-in').forEach(el => {
        observer.observe(el);
    });

    console.log('✅ Intersection Observer initialized');
}

// Initialize optimized animations based on device capabilities
function initializeOptimizedAnimations() {
    // Skip complex animations on low-end devices or if user prefers reduced motion
    if (performanceMetrics.isLowEndDevice || performanceMetrics.prefersReducedMotion) {
        document.documentElement.style.setProperty('--animation-duration-fast', '0.01ms');
        document.documentElement.style.setProperty('--animation-duration-normal', '0.01ms');
        document.documentElement.style.setProperty('--animation-duration-slow', '0.01ms');
        console.log('⚡ Animations disabled for performance');
        return;
    }

    // Enable gradient text animation only on capable devices
    const gradientElements = document.querySelectorAll('.animate-gradient-text');
    gradientElements.forEach(el => {
        if (!performanceMetrics.isMobile) {
            el.style.willChange = 'background-position';
        }
    });

    console.log('✅ Optimized animations initialized');
}

// Initialize fingerprint click effects (only on capable devices)
function initializeFingerprintEffects() {
    const fingerprintClickable = document.querySelector('.fingerprint-clickable');
    if (!fingerprintClickable) return;

    let clickCount = 0;
    const maxClicks = 5; // Limit to prevent performance issues

    fingerprintClickable.addEventListener('click', throttle(() => {
        if (clickCount >= maxClicks) return;
        
        clickCount++;
        createOptimizedRippleEffect();
        
        // Reset click count after delay
        setTimeout(() => {
            clickCount = Math.max(0, clickCount - 1);
        }, 2000);
    }, 300));

    console.log('✅ Fingerprint effects initialized');
}

// Optimized ripple effect
function createOptimizedRippleEffect() {
    const container = document.querySelector('.fingerprint-ripple-container');
    if (!container) return;

    // Create single ripple instead of multiple for better performance
    const ripple = document.createElement('div');
    ripple.className = 'position-absolute rounded-circle border';
    ripple.style.cssText = `
        width: 100px;
        height: 100px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0);
        border-color: rgba(168, 85, 247, 0.5);
        animation: ripple-effect 1s ease-out forwards;
        pointer-events: none;
    `;

    container.appendChild(ripple);

    // Remove after animation
    setTimeout(() => {
        ripple.remove();
    }, 1000);
}

// Initialize 3D effects (desktop only)
function initialize3DEffects() {
    if (performanceMetrics.isMobile) return;

    const logo3dContainer = document.querySelector('.logo-3d-container');
    if (!logo3dContainer) return;

    const logoParent = logo3dContainer.parentElement;
    if (!logoParent) return;

    const handleMouseMove = throttle((e) => {
        const rect = logoParent.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const rotateY = ((e.clientX - centerX) / (rect.width / 2)) * 8; // Reduced intensity
        const rotateX = -((e.clientY - centerY) / (rect.height / 2)) * 5;

        requestAnimationFrame(() => {
            logo3dContainer.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });
    }, 16); // ~60fps

    const handleMouseLeave = () => {
        requestAnimationFrame(() => {
            logo3dContainer.style.transform = '';
        });
    };

    logoParent.addEventListener('mousemove', handleMouseMove);
    logoParent.addEventListener('mouseleave', handleMouseLeave);

    console.log('✅ 3D effects initialized');
}

// Performance monitoring
function initializePerformanceMonitoring() {
    // Monitor frame rate
    let frameCount = 0;
    let lastTime = performance.now();

    function measureFPS() {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
            const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
            
            if (fps < 30 && !performanceMetrics.isLowEndDevice) {
                console.warn(`⚠️ Low FPS detected: ${fps}fps - Consider reducing animations`);
                // Automatically reduce animations if FPS is too low
                document.documentElement.classList.add('reduce-animations');
            }
            
            frameCount = 0;
            lastTime = currentTime;
        }
        
        requestAnimationFrame(measureFPS);
    }

    // Start FPS monitoring only in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        requestAnimationFrame(measureFPS);
    }

    // Monitor memory usage (if available)
    if ('memory' in performance) {
        setInterval(() => {
            const memInfo = performance.memory;
            if (memInfo.usedJSHeapSize > memInfo.jsHeapSizeLimit * 0.9) {
                console.warn('⚠️ High memory usage detected');
            }
        }, 10000);
    }

    console.log('✅ Performance monitoring initialized');
}

// Update current year
function updateCurrentYear() {
    const yearElement = document.getElementById('current-year');
    if (yearElement) {
        yearElement.textContent = new Date().getFullYear();
    }
}

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple-effect {
        to {
            transform: translate(-50%, -50%) scale(2);
            opacity: 0;
        }
    }
    
    .reduce-animations * {
        animation-duration: 0.01ms !important;
        transition-duration: 0.01ms !important;
    }
`;
document.head.appendChild(style);

// Export for potential use by other scripts
window.NashopPerformance = {
    metrics: performanceMetrics,
    debounce,
    throttle
};

console.log('📦 Performance utilities loaded');
