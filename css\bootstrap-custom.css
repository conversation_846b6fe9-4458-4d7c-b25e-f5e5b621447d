/* Bootstrap 5 Custom Theme for Nashop - Performance Optimized */

/* CSS Custom Properties for consistent theming */
:root {
  --bs-primary: #a855f7;
  --bs-primary-rgb: 168, 85, 247;
  --bs-secondary: #d946ef;
  --bs-secondary-rgb: 217, 70, 239;
  --bs-info: #06b6d4;
  --bs-info-rgb: 6, 182, 212;
  --bs-warning: #f59e0b;
  --bs-warning-rgb: 245, 158, 11;
  --bs-dark: #000000;
  --bs-dark-rgb: 0, 0, 0;
  --bs-light: #e5e7eb;
  --bs-light-rgb: 229, 231, 235;
  
  /* Custom gradient colors */
  --gradient-primary: linear-gradient(45deg, var(--bs-primary), var(--bs-secondary));
  --gradient-info: linear-gradient(45deg, var(--bs-info), var(--bs-primary));
  --gradient-purple-cyan: linear-gradient(90deg, #a855f7, #06b6d4);
  --gradient-cyan-purple: linear-gradient(90deg, #06b6d4, #a855f7);
  
  /* Performance optimizations */
  --animation-duration-fast: 0.2s;
  --animation-duration-normal: 0.5s;
  --animation-duration-slow: 1s;
}

/* Performance optimizations - Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Mobile-first performance optimizations */
@media (max-width: 768px) {
  /* Disable complex animations on mobile */
  .animate-spin-slow,
  .animate-ping-slow,
  .animate-shooting-star,
  .animate-twinkle {
    animation: none !important;
  }
  
  /* Reduce transform complexity on mobile */
  .logo-3d-container {
    transform: none !important;
  }
}

/* Custom Bootstrap button styles */
.btn-primary {
  background: var(--gradient-primary);
  border: none;
  transition: transform var(--animation-duration-fast) ease, box-shadow var(--animation-duration-fast) ease;
}

.btn-primary:hover {
  background: var(--gradient-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.3);
}

.btn-info {
  background: var(--gradient-info);
  border: none;
  transition: transform var(--animation-duration-fast) ease, box-shadow var(--animation-duration-fast) ease;
}

.btn-info:hover {
  background: var(--gradient-info);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

.btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Custom text gradients */
.text-gradient-purple-cyan {
  background: var(--gradient-purple-cyan);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-cyan-purple {
  background: var(--gradient-cyan-purple);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Optimized animations - GPU accelerated */
.animate-gradient-text {
  background: linear-gradient(45deg, #a855f7, #d946ef, #06b6d4, #a855f7);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease-in-out infinite;
  will-change: background-position;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.animate-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--animation-duration-normal) ease, transform var(--animation-duration-normal) ease;
}

.animate-fade-in.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Optimized spin animations */
.animate-spin-slow {
  animation: spin 20s linear infinite;
  will-change: transform;
}

.animate-ping-slow {
  animation: ping 3s cubic-bezier(0, 0, 0.2, 1) infinite;
  will-change: transform, opacity;
}

.animate-twinkle {
  animation: twinkle 6s ease-in-out infinite;
  will-change: opacity;
}

.animate-shooting-star {
  animation: shooting-star 4s ease-out infinite;
  will-change: transform, opacity;
}

/* Keyframes for animations */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

@keyframes shooting-star {
  0% {
    transform: translateX(-100px) translateY(-100px) rotate(15deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw) translateY(100px) rotate(15deg);
    opacity: 0;
  }
}

/* Button icon animations */
.btn-icon {
  transition: transform var(--animation-duration-fast) ease;
}

.btn:hover .btn-icon {
  transform: scale(1.1);
}

/* Fingerprint icon styles */
.fingerprint-icon {
  transition: all var(--animation-duration-normal) ease;
  will-change: transform, filter;
}

.fingerprint-clickable:hover .fingerprint-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 25px rgba(255,255,255,0.7));
  color: white !important;
}

/* Performance containment */
.hero-section {
  contain: layout style paint;
}

.logo-container {
  contain: layout style;
}

/* Reduced motion fallbacks */
@media (prefers-reduced-motion: reduce) {
  .animate-gradient-text {
    background: var(--bs-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: none;
  }
}

/* Loading states */
.loading-spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: var(--bs-primary);
  animation: spin 1s ease-in-out infinite;
}

/* Utility classes for performance */
.will-change-transform { will-change: transform; }
.will-change-opacity { will-change: opacity; }
.gpu-accelerated { transform: translateZ(0); }

/* Dark theme optimizations */
[data-bs-theme="dark"] {
  color-scheme: dark;
}

[data-bs-theme="dark"] .btn-outline-light {
  --bs-btn-color: #e5e7eb;
  --bs-btn-border-color: rgba(229, 231, 235, 0.3);
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: rgba(229, 231, 235, 0.1);
  --bs-btn-hover-border-color: rgba(229, 231, 235, 0.5);
}

/* Responsive typography improvements */
@media (max-width: 576px) {
  .hero-title {
    font-size: 3rem !important;
    line-height: 1.1;
  }
  
  .hero-subtitle {
    font-size: 1.1rem !important;
    line-height: 1.4;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .hero-title {
    font-size: 4rem !important;
  }
  
  .hero-subtitle {
    font-size: 1.3rem !important;
  }
}

/* Accessibility improvements */
@media (prefers-contrast: high) {
  .text-gradient-purple-cyan,
  .text-gradient-cyan-purple,
  .animate-gradient-text {
    background: white;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

/* Focus states for accessibility */
.fingerprint-clickable:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 4px;
  border-radius: 50%;
}

/* Print styles */
@media print {
  .animate-spin-slow,
  .animate-ping-slow,
  .animate-shooting-star,
  .animate-twinkle,
  .animate-gradient-text {
    animation: none !important;
  }
}
